package com.ksyun.security.test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * Java反射setAccessible漏洞利用演示
 * 
 * 警告：此代码仅用于安全测试和漏洞演示，请勿用于恶意攻击
 * 
 * <AUTHOR> Research Team
 * @date 2025-08-01
 */
public class ReflectionExploitDemo {

    /**
     * 模拟Spring框架内部敏感类
     */
    static class MockSpringSecurityContext {
        private String sessionId = "JSESSIONID_123456789";
        private String userToken = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9";
        private Map<String, Object> securityAttributes = new HashMap<>();
        private boolean authenticated = true;
        
        public MockSpringSecurityContext() {
            securityAttributes.put("username", "admin");
            securityAttributes.put("roles", new String[]{"ADMIN", "USER"});
            securityAttributes.put("permissions", new String[]{"READ", "WRITE", "DELETE"});
        }
        
        // 正常情况下，这些方法不会暴露敏感信息
        public boolean isAuthenticated() {
            return authenticated;
        }
    }

    /**
     * 模拟数据库连接池配置
     */
    static class MockDatabaseConfig {
        private String jdbcUrl = "****************************************";
        private String username = "db_admin";
        private String password = "prod_db_password_2025";
        private int maxConnections = 100;
        private boolean sslEnabled = true;
        
        public int getMaxConnections() {
            return maxConnections;
        }
    }

    /**
     * 模拟系统内部缓存
     */
    static class MockSystemCache {
        private Map<String, String> userSessions = new HashMap<>();
        private Map<String, Object> configCache = new HashMap<>();
        private String encryptionKey = "AES_256_SYSTEM_KEY_2025";
        
        public MockSystemCache() {
            userSessions.put("user1", "session_token_1");
            userSessions.put("admin", "admin_session_token");
            configCache.put("api_key", "sk-1234567890abcdef");
            configCache.put("secret_salt", "random_salt_value");
        }
    }

    public static void main(String[] args) {
        System.out.println("=== Java反射setAccessible漏洞利用演示 ===");
        System.out.println("警告：此演示仅用于安全测试目的\n");
        
        // 演示1: 访问Spring Security上下文
        demonstrateSpringSecurityExploit();
        
        // 演示2: 访问数据库配置信息
        demonstrateDatabaseConfigExploit();
        
        // 演示3: 访问系统缓存数据
        demonstrateSystemCacheExploit();
        
        // 演示4: 修改私有字段值
        demonstrateFieldModification();
        
        // 演示5: 调用私有方法
        demonstratePrivateMethodInvocation();
        
        System.out.println("\n=== 漏洞利用演示完成 ===");
        System.out.println("🚨 风险提示: 以上所有操作都绕过了Java的访问控制机制");
        System.out.println("🛡️ 建议: 立即修复CommonUtils中的setAccessible使用");
    }

    /**
     * 演示1: 利用反射访问Spring Security敏感信息
     */
    private static void demonstrateSpringSecurityExploit() {
        System.out.println("=== 演示1: Spring Security上下文攻击 ===");
        
        try {
            MockSpringSecurityContext securityContext = new MockSpringSecurityContext();
            
            // 正常访问 - 只能获取有限信息
            System.out.println("正常访问结果: authenticated = " + securityContext.isAuthenticated());
            
            // 恶意反射访问 - 获取所有敏感信息
            Class<?> clazz = MockSpringSecurityContext.class;
            Field[] fields = clazz.getDeclaredFields();
            
            System.out.println("\n🚨 恶意反射访问结果:");
            for (Field field : fields) {
                field.setAccessible(true); // 绕过访问控制
                Object value = field.get(securityContext);
                System.out.println("  " + field.getName() + " = " + value);
            }
            
        } catch (Exception e) {
            System.out.println("演示1异常: " + e.getMessage());
        }
        
        System.out.println("✅ 演示1完成: 成功获取Spring Security敏感信息\n");
    }

    /**
     * 演示2: 利用反射访问数据库配置
     */
    private static void demonstrateDatabaseConfigExploit() {
        System.out.println("=== 演示2: 数据库配置信息泄露 ===");
        
        try {
            MockDatabaseConfig dbConfig = new MockDatabaseConfig();
            
            // 正常访问
            System.out.println("正常访问结果: maxConnections = " + dbConfig.getMaxConnections());
            
            // 恶意反射访问数据库敏感配置
            System.out.println("\n🚨 数据库敏感配置泄露:");
            Field[] fields = MockDatabaseConfig.class.getDeclaredFields();
            
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(dbConfig);
                
                if (field.getName().contains("password") || 
                    field.getName().contains("username") || 
                    field.getName().contains("jdbcUrl")) {
                    System.out.println("  🔓 " + field.getName() + " = " + value);
                }
            }
            
        } catch (Exception e) {
            System.out.println("演示2异常: " + e.getMessage());
        }
        
        System.out.println("✅ 演示2完成: 数据库凭据完全泄露\n");
    }

    /**
     * 演示3: 利用反射访问系统缓存
     */
    private static void demonstrateSystemCacheExploit() {
        System.out.println("=== 演示3: 系统缓存数据窃取 ===");
        
        try {
            MockSystemCache systemCache = new MockSystemCache();
            
            // 恶意访问系统缓存
            Field[] fields = MockSystemCache.class.getDeclaredFields();
            
            System.out.println("🚨 系统缓存数据窃取:");
            for (Field field : fields) {
                field.setAccessible(true);
                Object value = field.get(systemCache);
                
                System.out.println("  📦 " + field.getName() + " = " + value);
                
                // 如果是Map类型，进一步展示内容
                if (value instanceof Map) {
                    Map<?, ?> mapValue = (Map<?, ?>) value;
                    mapValue.forEach((k, v) -> 
                        System.out.println("    └─ " + k + " = " + v));
                }
            }
            
        } catch (Exception e) {
            System.out.println("演示3异常: " + e.getMessage());
        }
        
        System.out.println("✅ 演示3完成: 系统缓存完全暴露\n");
    }

    /**
     * 演示4: 修改私有字段值
     */
    private static void demonstrateFieldModification() {
        System.out.println("=== 演示4: 恶意修改私有字段 ===");
        
        try {
            MockDatabaseConfig dbConfig = new MockDatabaseConfig();
            
            // 显示原始值
            Field passwordField = MockDatabaseConfig.class.getDeclaredField("password");
            passwordField.setAccessible(true);
            String originalPassword = (String) passwordField.get(dbConfig);
            System.out.println("原始密码: " + originalPassword);
            
            // 恶意修改密码
            passwordField.set(dbConfig, "hacked_password_123");
            String modifiedPassword = (String) passwordField.get(dbConfig);
            System.out.println("🚨 密码已被恶意修改: " + modifiedPassword);
            
            // 修改认证状态
            MockSpringSecurityContext securityContext = new MockSpringSecurityContext();
            Field authField = MockSpringSecurityContext.class.getDeclaredField("authenticated");
            authField.setAccessible(true);
            
            System.out.println("原始认证状态: " + authField.get(securityContext));
            authField.set(securityContext, false);
            System.out.println("🚨 认证状态被恶意修改: " + authField.get(securityContext));
            
        } catch (Exception e) {
            System.out.println("演示4异常: " + e.getMessage());
        }
        
        System.out.println("✅ 演示4完成: 私有字段恶意修改成功\n");
    }

    /**
     * 演示5: 调用私有方法
     */
    private static void demonstratePrivateMethodInvocation() {
        System.out.println("=== 演示5: 私有方法调用攻击 ===");
        
        try {
            // 创建一个包含私有方法的测试类
            class SecretService {
                private String secretKey = "ultra_secret_key";
                
                private String getSecretData() {
                    return "This is highly confidential data: " + secretKey;
                }
                
                private void dangerousOperation() {
                    System.out.println("🚨 执行了危险的私有操作!");
                }
            }
            
            SecretService service = new SecretService();
            
            // 恶意调用私有方法
            Method secretMethod = SecretService.class.getDeclaredMethod("getSecretData");
            secretMethod.setAccessible(true);
            String secretData = (String) secretMethod.invoke(service);
            System.out.println("🚨 私有方法调用结果: " + secretData);
            
            Method dangerousMethod = SecretService.class.getDeclaredMethod("dangerousOperation");
            dangerousMethod.setAccessible(true);
            dangerousMethod.invoke(service);
            
        } catch (Exception e) {
            System.out.println("演示5异常: " + e.getMessage());
        }
        
        System.out.println("✅ 演示5完成: 私有方法调用攻击成功\n");
    }
}
