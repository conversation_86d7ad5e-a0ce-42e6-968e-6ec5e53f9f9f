@echo off
chcp 65001 >nul
echo ========================================
echo Java反射漏洞快速测试
echo ========================================
echo.

cd /d "%~dp0"

echo [信息] 正在编译测试代码...
call mvn clean compile -q
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 编译失败，请检查代码
    pause
    exit /b 1
)

echo [成功] 编译完成
echo.

echo [信息] 正在运行漏洞利用演示...
echo ========================================
call mvn exec:java -Dexec.mainClass="com.ksyun.security.test.ReflectionExploitDemo" -q

echo.
echo ========================================
echo [信息] 正在运行安全修复演示...
echo ========================================
call mvn exec:java -Dexec.mainClass="com.ksyun.security.test.SecurityFixRecommendation" -q

echo.
echo ========================================
echo [信息] 快速测试完成
echo ========================================
echo.
echo 🚨 重要发现:
echo   1. CommonUtils方法存在反射访问漏洞
echo   2. 可以绕过Java访问控制机制
echo   3. 能够访问和修改私有敏感字段
echo.
echo 🛡️ 修复建议:
echo   1. 使用SecurityFixRecommendation中的安全实现
echo   2. 启用SecurityManager
echo   3. 实施字段访问白名单机制
echo.
pause
