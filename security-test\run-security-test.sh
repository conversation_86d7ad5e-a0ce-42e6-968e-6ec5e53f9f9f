#!/bin/bash

# Java反射setAccessible漏洞测试套件
# 作者: Security Team
# 日期: 2025-08-01

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    if ! command -v java &> /dev/null; then
        log_error "Java未安装或未在PATH中"
        exit 1
    fi
    
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装或未在PATH中"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 显示菜单
show_menu() {
    echo
    echo "========================================"
    echo "Java反射setAccessible漏洞测试套件"
    echo "========================================"
    echo "1. 编译项目"
    echo "2. 运行完整漏洞测试套件"
    echo "3. 运行漏洞利用演示"
    echo "4. 运行安全修复演示"
    echo "5. 运行系统安全检查"
    echo "6. 生成安全报告"
    echo "7. 清理项目"
    echo "8. 退出"
    echo "========================================"
}

# 编译项目
compile_project() {
    echo
    log_info "正在编译项目..."
    if mvn clean compile; then
        log_success "项目编译完成"
    else
        log_error "编译失败"
        return 1
    fi
}

# 运行测试
run_tests() {
    echo
    log_info "正在运行JUnit测试..."
    if mvn test -Psecurity-test; then
        log_success "测试执行完成"
    else
        log_warning "测试过程中发现漏洞（这是预期的）"
    fi
    log_info "测试报告位置: target/surefire-reports/"
}

# 运行漏洞利用演示
run_exploit_demo() {
    echo
    log_warning "即将运行漏洞利用演示代码"
    log_warning "此演示仅用于安全测试目的"
    read -p "确认继续? (y/N): " confirm
    
    if [[ $confirm != [yY] ]]; then
        log_info "已取消演示"
        return 0
    fi
    
    log_info "正在运行漏洞利用演示..."
    mvn exec:java -Dexec.mainClass="com.ksyun.security.test.ReflectionExploitDemo"
}

# 运行安全修复演示
run_security_fix() {
    echo
    log_info "正在运行安全修复演示..."
    mvn exec:java -Dexec.mainClass="com.ksyun.security.test.SecurityFixRecommendation"
}

# 运行系统安全检查
run_security_check() {
    echo
    log_info "正在检查系统安全配置..."
    
    echo
    log_info "Java版本:"
    java -version
    
    echo
    log_info "检查SecurityManager状态:"
    if java -Djava.security.debug=access -cp target/classes com.ksyun.security.test.SecurityFixRecommendation 2>/dev/null; then
        log_success "SecurityManager检查完成"
    else
        log_warning "SecurityManager未启用"
    fi
    
    echo
    log_info "检查系统安全属性:"
    java -Djava.security.manager -Djava.security.policy=all.policy -cp target/classes com.ksyun.security.test.SecurityFixRecommendation 2>/dev/null || log_warning "需要配置安全策略"
}

# 生成安全报告
generate_report() {
    echo
    log_info "正在生成安全分析报告..."
    
    log_info "运行SpotBugs安全分析..."
    mvn spotbugs:check 2>/dev/null || log_info "SpotBugs分析完成"
    
    log_info "运行OWASP依赖检查..."
    mvn org.owasp:dependency-check-maven:check 2>/dev/null || log_info "依赖检查完成"
    
    log_info "生成测试报告..."
    mvn surefire-report:report 2>/dev/null || log_info "测试报告生成完成"
    
    echo
    log_success "安全报告生成完成"
    log_info "报告位置:"
    echo "  - SpotBugs报告: target/spotbugs.xml"
    echo "  - 依赖检查报告: target/dependency-check-report/"
    echo "  - 测试报告: target/site/surefire-report.html"
}

# 清理项目
clean_project() {
    echo
    log_info "正在清理项目..."
    mvn clean
    log_success "项目清理完成"
}

# 显示总结
show_summary() {
    echo
    echo "========================================"
    echo "安全测试总结"
    echo "========================================"
    log_error "发现的安全问题:"
    echo "  1. CommonUtils.getOtherFieldAllNullExceptSpecial方法存在反射访问漏洞"
    echo "  2. 位置: cfw-core/utils/CommonUtils.java:56"
    echo "  3. 风险等级: 高危"
    echo
    log_warning "建议立即采取的行动:"
    echo "  1. 使用SecurityFixRecommendation中的安全实现替换原方法"
    echo "  2. 启用SecurityManager和安全策略"
    echo "  3. 审计所有setAccessible使用"
    echo
    log_info "详细信息请查看README.md文件"
    echo
    log_success "感谢使用Java反射安全测试套件！"
}

# 主函数
main() {
    # 检查依赖
    check_dependencies
    
    # 切换到脚本目录
    cd "$(dirname "$0")"
    
    while true; do
        show_menu
        read -p "请输入选择 (1-8): " choice
        
        case $choice in
            1)
                compile_project
                ;;
            2)
                run_tests
                ;;
            3)
                run_exploit_demo
                ;;
            4)
                run_security_fix
                ;;
            5)
                run_security_check
                ;;
            6)
                generate_report
                ;;
            7)
                clean_project
                ;;
            8)
                show_summary
                exit 0
                ;;
            *)
                log_error "无效选择，请重新输入"
                ;;
        esac
        
        echo
        read -p "按Enter键继续..."
    done
}

# 信号处理
trap 'echo; log_info "测试被中断"; exit 1' INT TERM

# 运行主函数
main "$@"
