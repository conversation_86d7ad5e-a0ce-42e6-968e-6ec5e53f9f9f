<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ksyun</groupId>
    <artifactId>cfw-parent</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>

    <modules>
        <module>cfw-api</module>
        <module>cfw-message</module>
        <module>cfw-core</module>
        <module>cfw-scheduler</module>
    </modules>

    <name>cfw-parent</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>1.8</java.version>
        <dep.lombok.version>1.18.12</dep.lombok.version>
        <dep.spring.version>5.2.4.RELEASE</dep.spring.version>
        <net-commons.version>0.3.3-SNAPSHOT</net-commons.version>
        <dep.spring-boot.version>2.7.10</dep.spring-boot.version>
        <dep.network-log.version>1.0.41-Release</dep.network-log.version>
        <dep.spring-tx.version>5.2.4.RELEASE</dep.spring-tx.version>
        <dep.jackson.core.version>2.10.5</dep.jackson.core.version>
        <dep.spring.version>4.2.0.RELEASE</dep.spring.version>
        <dep.springloaded.version>1.2.8.RELEASE</dep.springloaded.version>
        <dep.spring-cloud-context.version>2.</dep.spring-cloud-context.version>
        <dep.ksyun-common.version>0.1.15</dep.ksyun-common.version>
        <dep.net-commonms>0.1.9-SNAPSHOT</dep.net-commonms>
        <dep.eclipselink.version>2.6.2</dep.eclipselink.version>
        <dep.redisson.version>3.21.3</dep.redisson.version>
        <dep.mybatis-plus-starter.version>3.5.2</dep.mybatis-plus-starter.version>
        <dep.mybatis-plus-generator.version>3.5.3</dep.mybatis-plus-generator.version>
        <dep.mysql-connector.version>8.0.27</dep.mysql-connector.version>
        <hutool-all.version>5.6.7</hutool-all.version>
        <dep.xxl-job.version>2.3.1</dep.xxl-job.version>
        <mapstruct.version>1.5.2.Final</mapstruct.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot</artifactId>
                <version>${dep.spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${dep.spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.hibernate.validator</groupId>
                        <artifactId>hibernate-validator</artifactId>
                    </exclusion>
                    <!-- 排除自带的logback依赖 -->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ksyun.common</groupId>
                <artifactId>network-log</artifactId>
                <version>${dep.network-log.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ksyun.net</groupId>
                <artifactId>net-commons-starter-redis-all</artifactId>
                <version>${net-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ksyun.net</groupId>
                <artifactId>net-commons-starter-apollo</artifactId>
                <version>${net-commons.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.logging.log4j</groupId>
                        <artifactId>log4j-slf4j-impl</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ksyun.net</groupId>
                <artifactId>net-commons-autoconfigure</artifactId>
                <version>${net-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ksyun.net</groupId>
                <artifactId>net-commons-core</artifactId>
                <version>${net-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${dep.lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-context</artifactId>
                <version>3.1.1</version>
            </dependency>
            <!-- spring-boot 2.2.5.RELEASE 依赖spring-tx 5.2.4.RELEASE ReactiveTransactionManager类 -->
            <!--   <dependency>
                   <groupId>org.springframework</groupId>
                   <artifactId>spring-tx</artifactId>
                   <version>${dep.spring-tx.version}</version>
               </dependency>-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-log4j2</artifactId>
                <version>${dep.spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${dep.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${dep.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${dep.jackson.core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-oxm</artifactId>
                <version>${dep.spring.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-beans</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${dep.spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>${dep.spring-boot.version}</version>
            </dependency>
            <!--      <dependency>
                      <groupId>org.springframework.boot</groupId>
                      <artifactId>spring-boot-starter-actuator</artifactId>
                      <version>${dep.spring-boot.version}</version>
                  </dependency>-->

            <dependency>
                <groupId>org.eclipse.persistence</groupId>
                <artifactId>eclipselink</artifactId>
                <version>${dep.eclipselink.version}</version>
            </dependency>

            <!-- 热插拔组件引入 -->
            <dependency>
                <groupId>com.ksyun.net</groupId>
                <artifactId>net-commons-trade-core</artifactId>
                <version>${net-commons.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ksyun.net</groupId>
                <artifactId>net-commons-starter-platform-all</artifactId>
                <version>${net-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ksyun.net</groupId>
                <artifactId>net-commons-mq-v2</artifactId>
                <version>${net-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <scope>test</scope>
                <version>${dep.spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${dep.redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${dep.mysql-connector.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-jdbc</artifactId>
                <version>2.5.0</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${dep.mybatis-plus-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${dep.mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>2.2.5.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.ksyun</groupId>
                <artifactId>ksyun-sdk-java-common</artifactId>
                <version>0.0.2</version>
            </dependency>
            <dependency>
                <groupId>io.etcd</groupId>
                <artifactId>jetcd-core</artifactId>
                <version>0.5.9</version>
            </dependency>
            <!-- 添加 Guava 依赖 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>30.1-jre</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${dep.xxl-job.version}</version>
            </dependency>

            <!--MapStruct依赖start-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!--MapStruct依赖end-->
            <dependency>
                <groupId>org.springframework.kafka</groupId>
                <artifactId>spring-kafka</artifactId>
                <version>2.7.8</version>
            </dependency>
            <dependency>
                <groupId>com.ksyun</groupId>
                <artifactId>ks3-kss-java-sdk</artifactId>
                <version>1.0.6</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <build>
        <defaultGoal>compile</defaultGoal>
    </build>
</project>