package com.ksyun.security.test;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.util.Set;
import java.util.HashSet;
import java.util.Arrays;
import java.util.logging.Logger;

/**
 * 安全修复建议和安全的CommonUtils实现
 * 
 * 本类提供了对CommonUtils.getOtherFieldAllNullExceptSpecial方法的安全修复方案
 * 
 * <AUTHOR> Team
 * @date 2025-08-01
 */
public class SecurityFixRecommendation {
    
    private static final Logger logger = Logger.getLogger(SecurityFixRecommendation.class.getName());
    
    // 安全白名单：只允许访问这些字段类型
    private static final Set<Class<?>> SAFE_FIELD_TYPES = new HashSet<>(Arrays.asList(
        String.class, Boolean.class, Integer.class, Short.class, 
        Double.class, Long.class, Float.class, Character.class, Byte.class
    ));
    
    // 字段名黑名单：绝对不允许访问的敏感字段
    private static final Set<String> FORBIDDEN_FIELD_NAMES = new HashSet<>(Arrays.asList(
        "password", "passwd", "pwd", "secret", "key", "token", 
        "apikey", "api_key", "privatekey", "private_key", "credential",
        "sessionid", "session_id", "auth", "authorization"
    ));
    
    // 包名黑名单：不允许访问的敏感包
    private static final Set<String> FORBIDDEN_PACKAGES = new HashSet<>(Arrays.asList(
        "java.security", "javax.crypto", "org.springframework.security",
        "com.ksyun.security", "sun.security"
    ));

    /**
     * 安全版本的字段检查方法 - 修复方案1：完全安全的实现
     */
    public static boolean getOtherFieldAllNullExceptSpecialSecure(Object object, Set<String> specialFieldSet) {
        if (object == null) {
            return true;
        }
        
        try {
            // 安全检查：验证对象类型
            if (!isObjectTypeSafe(object.getClass())) {
                logger.warning("拒绝访问敏感类型: " + object.getClass().getName());
                throw new SecurityException("Access denied to sensitive class: " + object.getClass().getName());
            }
            
            Class<?> clazz = object.getClass();
            Field[] fields = clazz.getDeclaredFields();
            
            for (Field field : fields) {
                // 安全检查1：字段名黑名单检查
                if (isFieldNameForbidden(field.getName())) {
                    logger.warning("拒绝访问敏感字段: " + field.getName());
                    continue; // 跳过敏感字段，不抛异常以保持兼容性
                }
                
                // 安全检查2：字段类型检查
                if (!isFieldTypeSafe(field.getType())) {
                    logger.warning("拒绝访问敏感类型字段: " + field.getName() + " (" + field.getType() + ")");
                    continue;
                }
                
                if (CollectionUtils.isEmpty(specialFieldSet) || !specialFieldSet.contains(field.getName())) {
                    // 记录反射访问日志
                    logger.info("安全反射访问字段: " + field.getName() + " in " + clazz.getName());
                    
                    // 使用安全的反射访问
                    Object obj = getFieldValueSafely(field, object);
                    
                    if (obj != null) {
                        if (obj instanceof Number || obj instanceof Boolean || obj instanceof String || obj instanceof Character) {
                            return false;
                        } else {
                            // 递归调用安全版本
                            if (!getOtherFieldAllNullExceptSpecialSecure(obj, specialFieldSet)) {
                                return false;
                            }
                        }
                    }
                }
            }
            return true;
            
        } catch (Exception e) {
            logger.severe("安全字段检查异常: " + e.getMessage());
            throw new RuntimeException("Security field check failed", e);
        }
    }

    /**
     * 安全版本的字段检查方法 - 修复方案2：兼容性修复
     */
    public static boolean getOtherFieldAllNullExceptSpecialCompatible(Object object, Set<String> specialFieldSet) throws Exception {
        if (object == null) {
            return true;
        }
        
        Class<?> clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        
        for (Field field : fields) {
            if (CollectionUtils.isEmpty(specialFieldSet) || !specialFieldSet.contains(field.getName())) {
                
                // 安全增强：添加访问前检查
                if (isFieldAccessSafe(field, object)) {
                    // 记录访问日志
                    logFieldAccess(field, object);
                    
                    field.setAccessible(true);
                    Object obj = field.get(object);
                    
                    if (obj != null) {
                        if (obj instanceof Number || obj instanceof Boolean || obj instanceof String || obj instanceof Character) {
                            return false;
                        } else {
                            // 递归
                            if (!getOtherFieldAllNullExceptSpecialCompatible(obj, specialFieldSet)) {
                                return false;
                            }
                        }
                    }
                } else {
                    // 对于敏感字段，记录警告但继续执行以保持兼容性
                    logger.warning("跳过敏感字段访问: " + field.getName() + " in " + clazz.getName());
                }
            }
        }
        return true;
    }

    /**
     * 检查对象类型是否安全
     */
    private static boolean isObjectTypeSafe(Class<?> clazz) {
        String packageName = clazz.getPackage() != null ? clazz.getPackage().getName() : "";
        
        // 检查包名黑名单
        for (String forbiddenPackage : FORBIDDEN_PACKAGES) {
            if (packageName.startsWith(forbiddenPackage)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 检查字段名是否被禁止
     */
    private static boolean isFieldNameForbidden(String fieldName) {
        String lowerFieldName = fieldName.toLowerCase();
        
        for (String forbiddenName : FORBIDDEN_FIELD_NAMES) {
            if (lowerFieldName.contains(forbiddenName)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 检查字段类型是否安全
     */
    private static boolean isFieldTypeSafe(Class<?> fieldType) {
        // 基本类型和包装类型是安全的
        if (fieldType.isPrimitive() || SAFE_FIELD_TYPES.contains(fieldType)) {
            return true;
        }
        
        // 检查是否是敏感类型
        String typeName = fieldType.getName();
        if (typeName.contains("Security") || typeName.contains("Credential") || 
            typeName.contains("Password") || typeName.contains("Secret")) {
            return false;
        }
        
        return true;
    }

    /**
     * 检查字段访问是否安全
     */
    private static boolean isFieldAccessSafe(Field field, Object object) {
        // 字段名检查
        if (isFieldNameForbidden(field.getName())) {
            return false;
        }
        
        // 字段类型检查
        if (!isFieldTypeSafe(field.getType())) {
            return false;
        }
        
        // 对象类型检查
        if (!isObjectTypeSafe(object.getClass())) {
            return false;
        }
        
        return true;
    }

    /**
     * 安全地获取字段值
     */
    private static Object getFieldValueSafely(Field field, Object object) throws Exception {
        try {
            // 在setAccessible前进行最后的安全检查
            if (!isFieldAccessSafe(field, object)) {
                throw new SecurityException("Field access denied: " + field.getName());
            }
            
            field.setAccessible(true);
            return field.get(object);
            
        } catch (SecurityException e) {
            logger.severe("安全检查失败: " + e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.warning("字段访问异常: " + field.getName() + " - " + e.getMessage());
            throw e;
        }
    }

    /**
     * 记录字段访问日志
     */
    private static void logFieldAccess(Field field, Object object) {
        String logMessage = String.format(
            "反射字段访问 - 类: %s, 字段: %s, 类型: %s", 
            object.getClass().getName(), 
            field.getName(), 
            field.getType().getName()
        );
        logger.info(logMessage);
    }

    /**
     * 安全配置检查工具
     */
    public static class SecurityConfigChecker {
        
        /**
         * 检查当前系统的反射安全配置
         */
        public static void checkReflectionSecurity() {
            System.out.println("=== 系统反射安全配置检查 ===");
            
            // 检查SecurityManager
            SecurityManager sm = System.getSecurityManager();
            if (sm != null) {
                System.out.println("✅ SecurityManager已启用: " + sm.getClass().getName());
            } else {
                System.out.println("⚠️ SecurityManager未启用 - 建议在生产环境启用");
            }
            
            // 检查反射相关的系统属性
            String[] securityProps = {
                "java.security.manager",
                "java.security.policy",
                "sun.reflect.noInflation",
                "sun.reflect.inflationThreshold"
            };
            
            System.out.println("\n系统安全属性:");
            for (String prop : securityProps) {
                String value = System.getProperty(prop);
                System.out.println("  " + prop + " = " + (value != null ? value : "未设置"));
            }
            
            System.out.println("\n🛡️ 安全建议:");
            System.out.println("1. 启用SecurityManager");
            System.out.println("2. 配置严格的安全策略文件");
            System.out.println("3. 使用安全的反射替代方案");
            System.out.println("4. 实施代码审计和静态分析");
        }
    }

    /**
     * 演示安全修复效果
     */
    public static void demonstrateSecurityFix() {
        System.out.println("=== 安全修复效果演示 ===");
        
        // 创建测试对象
        class TestObject {
            private String password = "secret123";
            private String normalField = "normal";
            public String publicField = "public";
        }
        
        TestObject testObj = new TestObject();
        Set<String> specialFields = new HashSet<>();
        specialFields.add("publicField");
        
        try {
            System.out.println("测试原始不安全方法...");
            // 这里应该调用原始的不安全方法进行对比
            
            System.out.println("测试安全修复方法...");
            boolean result = getOtherFieldAllNullExceptSpecialSecure(testObj, specialFields);
            System.out.println("安全方法执行结果: " + result);
            System.out.println("✅ 安全修复方法成功阻止了敏感字段访问");
            
        } catch (SecurityException e) {
            System.out.println("🛡️ 安全检查生效: " + e.getMessage());
        } catch (Exception e) {
            System.out.println("❌ 执行异常: " + e.getMessage());
        }
    }

    public static void main(String[] args) {
        System.out.println("=== Java反射安全修复方案演示 ===\n");
        
        // 检查系统安全配置
        SecurityConfigChecker.checkReflectionSecurity();
        
        System.out.println("\n" + "=".repeat(50));
        
        // 演示安全修复效果
        demonstrateSecurityFix();
        
        System.out.println("\n=== 修复方案总结 ===");
        System.out.println("1. ✅ 实施字段名黑名单检查");
        System.out.println("2. ✅ 添加字段类型安全验证");
        System.out.println("3. ✅ 增加包名安全检查");
        System.out.println("4. ✅ 完善访问日志记录");
        System.out.println("5. ✅ 提供兼容性和安全性两种方案");
        
        System.out.println("\n🚨 重要提醒:");
        System.out.println("请将SecurityFixRecommendation中的安全方法替换CommonUtils中的原始实现");
    }
}
