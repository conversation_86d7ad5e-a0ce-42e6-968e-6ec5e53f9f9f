@echo off
chcp 65001 >nul
echo ========================================
echo Java反射setAccessible漏洞测试套件
echo ========================================
echo.

set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

echo [信息] 当前目录: %CD%
echo [信息] 开始安全测试...
echo.

:MENU
echo ========================================
echo 请选择要执行的测试:
echo ========================================
echo 1. 编译项目
echo 2. 运行完整漏洞测试套件
echo 3. 运行漏洞利用演示
echo 4. 运行安全修复演示
echo 5. 运行系统安全检查
echo 6. 生成安全报告
echo 7. 清理项目
echo 8. 退出
echo ========================================
set /p choice=请输入选择 (1-8): 

if "%choice%"=="1" goto COMPILE
if "%choice%"=="2" goto RUN_TESTS
if "%choice%"=="3" goto RUN_EXPLOIT_DEMO
if "%choice%"=="4" goto RUN_SECURITY_FIX
if "%choice%"=="5" goto RUN_SECURITY_CHECK
if "%choice%"=="6" goto GENERATE_REPORT
if "%choice%"=="7" goto CLEAN
if "%choice%"=="8" goto EXIT

echo [错误] 无效选择，请重新输入
echo.
goto MENU

:COMPILE
echo.
echo ========================================
echo 编译项目
echo ========================================
echo [信息] 正在编译项目...
call mvn clean compile
if %ERRORLEVEL% NEQ 0 (
    echo [错误] 编译失败
    pause
    goto MENU
)
echo [成功] 项目编译完成
echo.
pause
goto MENU

:RUN_TESTS
echo.
echo ========================================
echo 运行完整漏洞测试套件
echo ========================================
echo [信息] 正在运行JUnit测试...
call mvn test -Psecurity-test
if %ERRORLEVEL% NEQ 0 (
    echo [警告] 测试过程中发现漏洞（这是预期的）
) else (
    echo [信息] 测试执行完成
)
echo.
echo [信息] 测试报告位置: target\surefire-reports\
pause
goto MENU

:RUN_EXPLOIT_DEMO
echo.
echo ========================================
echo 运行漏洞利用演示
echo ========================================
echo [警告] 即将运行漏洞利用演示代码
echo [警告] 此演示仅用于安全测试目的
set /p confirm=确认继续? (y/N): 
if /i not "%confirm%"=="y" (
    echo [信息] 已取消演示
    goto MENU
)

echo [信息] 正在运行漏洞利用演示...
call mvn exec:java -Dexec.mainClass="com.ksyun.security.test.ReflectionExploitDemo"
echo.
pause
goto MENU

:RUN_SECURITY_FIX
echo.
echo ========================================
echo 运行安全修复演示
echo ========================================
echo [信息] 正在运行安全修复演示...
call mvn exec:java -Dexec.mainClass="com.ksyun.security.test.SecurityFixRecommendation"
echo.
pause
goto MENU

:RUN_SECURITY_CHECK
echo.
echo ========================================
echo 运行系统安全检查
echo ========================================
echo [信息] 正在检查系统安全配置...

echo [检查] Java版本:
java -version

echo.
echo [检查] SecurityManager状态:
java -Djava.security.debug=access -cp target\classes com.ksyun.security.test.SecurityFixRecommendation 2>nul || echo SecurityManager未启用

echo.
echo [检查] 系统安全属性:
java -Djava.security.manager -Djava.security.policy=all.policy -cp target\classes com.ksyun.security.test.SecurityFixRecommendation 2>nul || echo 需要配置安全策略

echo.
pause
goto MENU

:GENERATE_REPORT
echo.
echo ========================================
echo 生成安全报告
echo ========================================
echo [信息] 正在生成安全分析报告...

echo [信息] 运行SpotBugs安全分析...
call mvn spotbugs:check 2>nul || echo SpotBugs分析完成

echo [信息] 运行OWASP依赖检查...
call mvn org.owasp:dependency-check-maven:check 2>nul || echo 依赖检查完成

echo [信息] 生成测试报告...
call mvn surefire-report:report 2>nul || echo 测试报告生成完成

echo.
echo [成功] 安全报告生成完成
echo [信息] 报告位置:
echo   - SpotBugs报告: target\spotbugs.xml
echo   - 依赖检查报告: target\dependency-check-report\
echo   - 测试报告: target\site\surefire-report.html
echo.
pause
goto MENU

:CLEAN
echo.
echo ========================================
echo 清理项目
echo ========================================
echo [信息] 正在清理项目...
call mvn clean
echo [成功] 项目清理完成
echo.
pause
goto MENU

:EXIT
echo.
echo ========================================
echo 安全测试总结
echo ========================================
echo [重要] 发现的安全问题:
echo   1. CommonUtils.getOtherFieldAllNullExceptSpecial方法存在反射访问漏洞
echo   2. 位置: cfw-core/utils/CommonUtils.java:56
echo   3. 风险等级: 高危
echo.
echo [建议] 立即采取的行动:
echo   1. 使用SecurityFixRecommendation中的安全实现替换原方法
echo   2. 启用SecurityManager和安全策略
echo   3. 审计所有setAccessible使用
echo.
echo [信息] 详细信息请查看README.md文件
echo.
echo 感谢使用Java反射安全测试套件！
pause
exit /b 0
